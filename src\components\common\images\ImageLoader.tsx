/**
 * Optimized Image Loader Component
 * 
 * This component handles image loading with the following optimizations:
 * 1. Unified image processing system for all image formats
 * 2. LRU caching strategy for memory management
 * 3. Web Worker processing for CPU-intensive operations
 * 4. Progressive loading and error handling
 * 5. Memory-efficient placeholder generation
 */

import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { Box } from '@mui/material';
import { unifiedImageService } from '@/services/image';
import {
  createImageCacheKey,
  getCachedImage,
  isImageCached,
  cacheImage
} from '@/services/image/image-cache';
import { unifiedErrorService, ErrorType } from '@/services/error';
import { getTraitImage } from '@/services/memory/image-persistence.service';

// Create an enhanced placeholder for large images
function createEnhancedPlaceholder(layerName: string, traitName: string): string {
  try {
    return unifiedImageService.createPlaceholder(layerName, traitName, {
      width: 400,
      height: 400,
      fontSize: 24,
      backgroundColor: '#f8f8f8',
      textColor: '#333333',
      text: `${layerName}\n${traitName}\n(Large Image)`
    });
  } catch (error) {
    // Fallback to simple placeholder if enhanced creation fails
    return unifiedImageService.createPlaceholder(layerName, traitName);
  }
}

interface ImageLoaderProps {
  src: string;
  alt: string;
  layerName: string;
  traitName: string;
  sx?: React.CSSProperties;
}

const ImageLoader: React.FC<ImageLoaderProps> = ({
  src,
  alt,
  layerName,
  traitName,
  sx = {}
}) => {
  // Create a unique id for this specific image instance
  const uniqueId = useRef(`img_${Math.random().toString(36).substr(2, 9)}`);
  const [imageSrc, setImageSrc] = useState<string>(
    unifiedImageService.createPlaceholder(layerName, traitName)
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const retryCount = useRef(0);
  const isMounted = useRef(true);
  const MAX_RETRIES = 2;
  
  // Reset state when image changes
  useEffect(() => {
    retryCount.current = 0;
    setError(false);
    setIsLoading(true);
    setImageSrc(unifiedImageService.createPlaceholder(layerName, traitName));
  }, [src, layerName, traitName]);
  
  // Cleanup function
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Load image function with proper memoization and better error handling
  const loadImage = useCallback(async () => {
    // Skip if no source
    if (!src) {
      setIsLoading(false);
      return;
    }
    
    // Debug info
    const debug = process.env.NODE_ENV === 'development';
    if (debug) {
      console.log(`Loading image: ${layerName}/${traitName} from ${src.substring(0, 30)}...`);
    }
    
    try {
      // First try to get from persistent image cache
      if (layerName && traitName) {
        const persistentImage = await getTraitImage(layerName, traitName);
        if (persistentImage && isMounted.current) {
          setImageSrc(persistentImage);
          setIsLoading(false);
          return;
        }
      }

      // Generate cache key - add the unique ID to avoid cache collisions
      const cacheKey = createImageCacheKey(src, {}, layerName, traitName) + '_' + uniqueId.current;

      // Check memory cache
      if (isImageCached(cacheKey)) {
        const cachedImage = getCachedImage(cacheKey);
        if (cachedImage && isMounted.current) {
          setImageSrc(typeof cachedImage === 'string' ?
            cachedImage :
            cachedImage.url || unifiedImageService.createPlaceholder(layerName, traitName)
          );
          setIsLoading(false);
        }
        return;
      }
      
      // Special handling for known problematic files
      if (layerName === "Eyewear" && (traitName === "Aviator" || traitName.includes("HUD"))) {
        // For known problematic large files, use enhanced placeholder
        const placeholderWithInfo = createEnhancedPlaceholder(layerName, traitName);
        if (isMounted.current) {
          setImageSrc(placeholderWithInfo);
          setIsLoading(false);
        }
        cacheImage(cacheKey, placeholderWithInfo);
        return;
      }
      
      try {
        // Process image with unified service
        const processedImage = await unifiedImageService.processImage(
          src,
          {
            maxDimension: 800,
            quality: 0.9,
            timeout: 10000
          },
          layerName,
          traitName
        );
        
        if (isMounted.current) {
          if (processedImage && processedImage.url) {
            // Cache the result
            cacheImage(cacheKey, processedImage);
            
            setImageSrc(processedImage.url);
            setIsLoading(false);
          } else {
            // Handle missing URL
            if (retryCount.current < MAX_RETRIES) {
              retryCount.current++;
              
              setTimeout(() => {
                if (isMounted.current) {
                  loadImage();
                }
              }, 500);
            } else {
              console.error(`Image processing failed (max retries): ${layerName}/${traitName}`);
              setIsLoading(false);
              setError(true);
            }
          }
        }
      } catch (error) {
        unifiedErrorService.reportError(error, {
          type: ErrorType.PROCESSING,
          context: {
            component: 'ImageLoader',
            layerName, 
            traitName,
            src: src.substring(0, 30)
          }
        });
        
        if (isMounted.current) {
          setIsLoading(false);
          setError(true);
          setImageSrc(unifiedImageService.createErrorPlaceholder(layerName, traitName, 'Load Error'));
        }
      }
    } catch (error) {
      unifiedErrorService.reportError(error, {
        type: ErrorType.UI,
        context: {
          component: 'ImageLoader',
          layerName, 
          traitName,
          src: src.substring(0, 30)
        }
      });
      
      if (isMounted.current) {
        setImageSrc(unifiedImageService.createPlaceholder(layerName, traitName));
        setIsLoading(false);
        setError(true);
      }
    }
  }, [src, layerName, traitName]);

  // Load image when required props change
  useEffect(() => {
    loadImage();
  }, [loadImage]);

  return (
    <Box
      key={uniqueId.current} // Add key for React reconciliation
      component="img"
      src={imageSrc}
      alt={alt}
      sx={{
        ...sx,
        opacity: isLoading ? 0.5 : 1,
        transition: 'opacity 0.3s ease-in-out',
        // Use relative position when explicitly set in parent props
        position: sx.position ? sx.position : 'absolute',
        // Only set these if we're using absolute positioning
        ...((!sx.position || sx.position === 'absolute') ? {
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
        } : {}),
        // Always maintain aspect ratio with contain
        objectFit: 'contain',
        maxWidth: '100%',
        maxHeight: '100%'
      }}
      data-layer={layerName} // Add data attributes for debugging
      data-trait={traitName}
    />
  );
};

// Use memo to prevent unnecessary re-renders, but ensure proper updates
export default memo(ImageLoader, (prevProps, nextProps) => {
  // Always re-render if any core properties change
  if (prevProps.src !== nextProps.src ||
      prevProps.layerName !== nextProps.layerName ||
      prevProps.traitName !== nextProps.traitName) {
    return false; // Re-render
  }
  
  // Check for sx changes that might indicate changes in z-index due to layer reordering
  const prevZIndex = prevProps.sx && 'zIndex' in prevProps.sx ? prevProps.sx.zIndex : undefined;
  const nextZIndex = nextProps.sx && 'zIndex' in nextProps.sx ? nextProps.sx.zIndex : undefined;
  
  if (prevZIndex !== nextZIndex) {
    return false; // Re-render
  }
  
  return true; // Don't re-render
});