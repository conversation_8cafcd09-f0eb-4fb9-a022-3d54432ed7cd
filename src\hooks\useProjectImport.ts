import { useRef, useState, useCallback, useEffect } from 'react';
import { useNFT } from '../contexts/NFTContext';
import { toast } from 'react-hot-toast';
import { ActionType } from '../contexts/actions/actionTypes';
import { v4 as uuidv4 } from 'uuid';
import { Layer, Trait } from '@/types';
import { analyzeImportedFiles } from '@/services/content-analysis/simple-relationship-analyzer';
import { useContentAnalysis } from './useContentAnalysis';
import { storeTraitImage } from '@/services/memory/image-persistence.service';

// İlerleme aşamaları için tip tanımı
type ImportStage =
  | 'idle'        // Hazır, henüz başlamamış
  | 'scanning'    // Klasör taranıyor
  | 'loading'     // Dosyalar yükleniyor
  | 'processing'  // Görüntüler işleniyor
  | 'organizing'  // Katmanlar düzenleniyor
  | 'finalizing'  // Son işlemler yapılıyor
  | 'done'        // Başarıyla tamamlandı
  | 'error';      // Hata oluştu

// İlerleme durum tipi
interface ImportProgress {
  stage: ImportStage;
  filesLoaded: number;
  totalFiles: number;
  percentage: number;
  currentFile?: string;
  message?: string;
}

export const useProjectImport = () => {
  const { state, dispatch } = useNFT();
  const contentAnalysis = useContentAnalysis();
  const [isImporting, setIsImporting] = useState<boolean>(false);
  const [importError, setImportError] = useState<string | null>(null);
  const [importSuccess, setImportSuccess] = useState<boolean>(false);

  // İlerleme durumu için state
  const [progress, setProgress] = useState<ImportProgress>({
    stage: 'idle',
    filesLoaded: 0,
    totalFiles: 0,
    percentage: 0
  });

  // Başvurular oluştur
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const importTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const layersLoadedRef = useRef<number>(0);
  const expectedLayersCountRef = useRef<number>(0);
  const retryAttemptsRef = useRef<number>(0);
  const forceUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Temizlik fonksiyonu - tüm timeout'ları temizle
  const cleanupTimeouts = useCallback(() => {
    console.log("Cleaning up all timeouts");

    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }

    if (importTimeoutRef.current) {
      clearTimeout(importTimeoutRef.current);
      importTimeoutRef.current = null;
    }

    if (forceUpdateTimeoutRef.current) {
      clearTimeout(forceUpdateTimeoutRef.current);
      forceUpdateTimeoutRef.current = null;
    }
  }, []);

  // İlerleme durumunu güncelleyen yardımcı fonksiyon - ÖNEMLİ: setupFailsafeTimeout fonksiyonundan ÖNCE tanımlandı
  const updateProgress = useCallback((
    stage: ImportStage,
    filesLoaded: number = 0,
    totalFiles: number = 0,
    additionalInfo?: { currentFile?: string, message?: string }
  ) => {
    const percentage = totalFiles > 0
      ? Math.min(Math.round((filesLoaded / totalFiles) * 100), 100)
      : 0;

    setProgress({
      stage,
      filesLoaded,
      totalFiles,
      percentage,
      ...(additionalInfo || {})
    });

    // Progress tracking (silent)
  }, []);

  // Failsafe timeout - her import işleminde maksimum 60 sn bekleyecek
  const setupFailsafeTimeout = useCallback(() => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    loadingTimeoutRef.current = setTimeout(() => {
      if (isImporting) {
        setImportError("İçe aktarma zaman aşımına uğradı. Lütfen tekrar deneyin veya daha küçük bir klasör seçin.");
        setIsImporting(false);

        // İlerleme durumunu hata olarak güncelle
        updateProgress('error', 0, 0, {
          message: "İçe aktarma zaman aşımına uğradı. Lütfen tekrar deneyin."
        });

        cleanupTimeouts();
      }
    }, 60000); // 60 sn timeout (daha büyük klasörler için)
  }, [isImporting, cleanupTimeouts, updateProgress]);

  // İmport başarılı olduğunda
  useEffect(() => {
    if (importSuccess && isImporting) {
      // Tüm bekleyen timeout'ları temizle
      cleanupTimeouts();

      // Loading durumunu kapat
      setIsImporting(false);

      // Başarı mesajı
      toast.success('Project imported successfully!', { id: 'import-toast' });

      // UI'ı yenilemeye zorla - küçük bir zaman aralığında iki farklı state güncellemesi
      forceUpdateTimeoutRef.current = setTimeout(() => {
        // importSuccess state'ini sıfırla (bir sonraki import için)
        setImportSuccess(false);

        // Ek güvenlik - forceUpdate patternini taklit etme
        dispatch({ type: ActionType.UPDATE_UI, payload: { timestamp: Date.now() } });

        // Değerleri sıfırla
        layersLoadedRef.current = 0;
        expectedLayersCountRef.current = 0;
        retryAttemptsRef.current = 0;

        // Import sonrası UI güncellemesini zorla
        dispatch({
          type: ActionType.SET_LAYERS,
          payload: [...state.layers]
        });
      }, 100);
    }
  }, [importSuccess, isImporting, dispatch, cleanupTimeouts, state.layers]);

  // Component unmount olduğunda temizlik yap
  useEffect(() => {
    return () => {
      cleanupTimeouts();
    };
  }, [cleanupTimeouts]);

  // Dosyayı base64 formatında okuma yardımcısı - geliştirilmiş hata yönetimi ile
  const readFileAsDataURL = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // Başarılı okuma
      reader.onload = () => {
        // Sonuç kontrolü
        if (!reader.result) {
          reject(new Error(`Dosya okunamadı: ${file.name}`));
          return;
        }

        // Sonucu string olarak dönüştür
        const result = reader.result as string;

        // Büyük dosyalar için kontrol (10MB'den büyük)
        if (file.size > 10 * 1024 * 1024) {
          console.warn(`Büyük dosya (${(file.size / (1024 * 1024)).toFixed(2)} MB): ${file.name}. Performans etkilenebilir.`);
        }

        resolve(result);
      };

      // Okuma hatası
      reader.onerror = () => {
        reject(new Error(`Dosya okuma hatası: ${file.name}`));
      };

      // Zaman aşımı kontrolü - 10 saniye
      const timeout = setTimeout(() => {
        reader.abort(); // Okumayı iptal et
        reject(new Error(`Dosya okuma zaman aşımı: ${file.name}`));
      }, 10000);

      // Okumayı başlat
      try {
        reader.readAsDataURL(file);
      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }

      // Tamamlandığında veya hata olduğunda zaman aşımını temizle
      reader.onloadend = () => {
        clearTimeout(timeout);
      };
    });
  };

  // Files import handler
  const processSelectedFiles = useCallback(async (files: FileList) => {
    try {
      // Temizlik ve hazırlık
      cleanupTimeouts();
      setImportError(null);
      setImportSuccess(false);
      setIsImporting(true);
      layersLoadedRef.current = 0;
      retryAttemptsRef.current = 0;

      // İlerleme durumunu başlat
      updateProgress('scanning', 0, files.length, { message: 'Klasörler ve dosyalar taranıyor...' });

      // Failsafe timeout ayarla
      setupFailsafeTimeout();

      // Before starting new import, check if a project with this folder path already exists
      if (files.length > 0) {
        // Get the root folder path
        const rootPath = files[0].webkitRelativePath.split('/')[0];

        // Store the path for project identification
        localStorage.setItem('last_imported_folder_path', rootPath);

        // Check if we have existing projects with this path
        const existingProjects = state.recentProjects || [];
        const matchingProject = existingProjects.find(p => p.folderPath === rootPath);

        if (matchingProject) {
          // Set this as the current project
          dispatch({
            type: ActionType.SET_CURRENT_PROJECT,
            payload: matchingProject
          });

          // Load all associated data
          // This will trigger the useEffect in useNFTDataLoader which will load all layers, rules, etc.

          // Complete the import process
          updateProgress('done', 1, 1, { message: 'Existing project loaded successfully!' });
          setImportSuccess(true);
          return;
        }

        // If no matching project, create a new one with the folder path
        dispatch({
          type: ActionType.SET_PROJECT_META,
          payload: {
            name: rootPath,
            path: rootPath,
            folderPath: rootPath
          }
        });
      }

      // Create an object to organize files by directories
      const directories: { [key: string]: File[] } = {};

      // Get the root folder path and store it
      if (files.length > 0) {
        const rootFolderPath = files[0].webkitRelativePath.split('/')[0];

        // Store the folder path for future project detection
        localStorage.setItem('last_imported_folder_path', rootFolderPath);

        // Create or update project with this folder path
        const existingProjects = state.recentProjects || [];
        const matchingProject = existingProjects.find(p => p.folderPath === rootFolderPath);

        if (matchingProject) {
          // Set this as the current project
          dispatch({
            type: ActionType.SET_CURRENT_PROJECT,
            payload: matchingProject
          });
        } else {
          // Create project metadata with folder path
          dispatch({
            type: ActionType.SET_PROJECT_META,
            payload: {
              name: rootFolderPath,
              path: rootFolderPath,
              folderPath: rootFolderPath
            }
          });
        }
      }

      // AŞAMA 1: TARAMA - Dosyaları dizinlere göre gruplandır
      updateProgress('scanning', 0, files.length, { message: 'Klasör yapısı analiz ediliyor...' });

      // Group files by directory path
      Array.from(files).forEach((file, index) => {
        // Her 100 dosyada bir ilerleme güncelle
        if (index % 100 === 0 || index === files.length - 1) {
          updateProgress('scanning', index + 1, files.length);
        }

        // Get the relative path from the input
        const relativePath = file.webkitRelativePath;
        if (!relativePath) return;

        // Extract directory path (first level only)
        const pathParts = relativePath.split('/');
        if (pathParts.length < 2) return;

        const dirName = pathParts[1]; // First level subdirectory

        // If this is not a file in a subdirectory or is a hidden file, skip
        if (pathParts.length < 3 || file.name.startsWith('.')) return;

        // Create directory entry if it doesn't exist
        if (!directories[dirName]) {
          directories[dirName] = [];
        }

        // Add file to directory
        directories[dirName].push(file);
      });

      // Katman sayısını tahmin et ve kaydet
      const directoryCount = Object.keys(directories).length;
      expectedLayersCountRef.current = directoryCount;

      if (directoryCount === 0) {
        throw new Error("No valid layer directories found. Please select a folder with the correct structure.");
      }

      // AŞAMA 2: YÜKLEME - Katmanları ve özellikleri oluştur
      updateProgress('loading', 0, directoryCount, { message: 'Katmanlar yükleniyor...' });

      // Create layers and traits from directories
      const layers: Layer[] = [];

      // Sort directory names to ensure consistent order
      const sortedDirNames = Object.keys(directories).sort();

      // Toplam işlenecek dosya sayısını hesapla (performans için)
      const totalImageFiles = Object.values(directories)
        .flatMap(files => files.filter(file => file.type.startsWith('image/')))
        .length;

      let processedImageCount = 0;

      // Process each directory as a layer
      for (let i = 0; i < sortedDirNames.length; i++) {
        const dirName = sortedDirNames[i];
        const dirFiles = directories[dirName];

        // Skip directories with no valid files
        if (!dirFiles || dirFiles.length === 0) continue;

        // İlerleme durumunu güncelle
        updateProgress('loading', i, sortedDirNames.length, {
          currentFile: dirName,
          message: `Katman yükleniyor: ${dirName}`
        });

        // Yüklenen katman sayısını artır
        layersLoadedRef.current++;

        // Create a new layer
        const layerId = uuidv4();
        const layer: Layer = {
          id: layerId,
          name: dirName,
          order: i + 1,
          traits: [],
          subgroups: [], // Initialize empty subgroups array
          rarityLocked: false, // Default to unlocked
          lockedRarity: 0, // Default locked rarity value
          visible: true, // Default to visible
        };

        // Group files by subfolder
        const subfolders: { [key: string]: File[] } = {};

        dirFiles.forEach(file => {
          // Skip non-image files
          if (!file.type.startsWith('image/')) return;

          const relativePath = file.webkitRelativePath;
          const pathParts = relativePath.split('/');

          // Get subfolder path (if any)
          let subfolderPath = '';
          if (pathParts.length > 3) {
            // Extract subfolder path (everything between layer and filename)
            subfolderPath = pathParts.slice(2, pathParts.length - 1).join('/');
          }

          if (!subfolders[subfolderPath]) {
            subfolders[subfolderPath] = [];
          }

          subfolders[subfolderPath].push(file);
        });

        // AŞAMA 3: İŞLEME - Dosyaları işle
        updateProgress('processing', processedImageCount, totalImageFiles, {
          currentFile: dirName,
          message: `Görüntüler işleniyor: ${dirName}`
        });

        // Process files in each subfolder
        for (const [subfolderPath, subfolderFiles] of Object.entries(subfolders)) {
          // Process each trait (image file)
          await Promise.all(subfolderFiles.map(async (file, fileIndex) => {
            try {
              if (!file.type.startsWith('image/')) return;

              // İlerleme güncelleme - her 5 dosyada bir
              processedImageCount++;
              if (processedImageCount % 5 === 0 || processedImageCount === totalImageFiles) {
                updateProgress('processing', processedImageCount, totalImageFiles, {
                  currentFile: file.name,
                  message: `Görüntü işleniyor: ${file.name}`
                });
              }

              // Store image in persistent cache and get URL
              const persistentImageUrl = await storeTraitImage(file, dirName, file.name.replace(/\.[^/.]+$/, ""));

              // Create a trait
              const trait: Trait = {
                id: uuidv4(),
                name: file.name.replace(/\.[^/.]+$/, ""), // Remove extension
                imageUrl: persistentImageUrl, // Use persistent URL instead of data URL
                path: subfolderPath ? `${subfolderPath}/${file.name}` : file.name,
                rarity: 1, // Minimum non-zero value, will be calculated later
                subfolderPath: subfolderPath || '', // Ensure subfolderPath is always a string
                subgroupId: '', // Will be set later when subgroups are created
                rarityLocked: false, // Default to unlocked
              };

              // Add trait to layer
              layer.traits.push(trait);
            } catch (error) {
              console.error("Error processing trait:", file.name, error);
            }
          }));
        }

        // Skip layers with no valid traits
        if (layer.traits.length === 0) {
          layersLoadedRef.current--; // Adjust for empty layer
          continue;
        }

        // AŞAMA 4: DÜZENLEME - Nadirlik değerlerini hesapla ve katmanı ekle
        updateProgress('organizing', i + 1, sortedDirNames.length, {
          message: `Katman organize ediliyor: ${dirName}`
        });

        // Calculate even distribution for traits with proper rounding to ensure total = 100%
        const traitCount = layer.traits.length;
        const evenRarity = 100 / traitCount;
        const roundedRarity = parseFloat(evenRarity.toFixed(2));

        // Assign equal rarity to all traits except the last one
        for (let i = 0; i < layer.traits.length - 1; i++) {
          layer.traits[i].rarity = roundedRarity;
        }

        // Calculate remaining rarity for the last trait to ensure total = 100%
        const usedRarity = roundedRarity * (layer.traits.length - 1);
        const remainingRarity = parseFloat((100 - usedRarity).toFixed(2));
        if (layer.traits.length > 0) {
          layer.traits[layer.traits.length - 1].rarity = remainingRarity;
        }

        // Create subgroups based on subfolder paths
        const subfolderPaths = new Set<string>();
        layer.traits.forEach(trait => {
          if (trait.subfolderPath && trait.subfolderPath.trim() !== '') {
            subfolderPaths.add(trait.subfolderPath);
          }
        });

        // Create a subgroup for each unique subfolder path
        if (subfolderPaths.size > 0) {
          // Create subgroups for each unique subfolder path
          Array.from(subfolderPaths).forEach(folderPath => {
            const subgroupId = uuidv4();
            const subgroupName = folderPath.split('/').pop() || 'Unnamed';

            // Get traits for this subgroup
            const subgroupTraits = layer.traits
              .filter(trait => trait.subfolderPath === folderPath)
              .map(trait => {
                // Update trait's subgroupId
                trait.subgroupId = subgroupId;
                return trait.id;
              });

            // Create the subgroup
            layer.subgroups.push({
              id: subgroupId,
              name: subgroupName,
              folderPath: folderPath,
              rarityWeight: (subgroupTraits.length / traitCount) * 100,
              traits: subgroupTraits,
              isLocked: false,
              lockedRarity: 0,
              metadata: {
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              }
            });
          });
        } else {
          // Create a default subgroup if no subfolders exist
          const defaultSubgroupId = uuidv4();

          // Assign all traits to the default subgroup
          layer.traits.forEach(trait => {
            trait.subgroupId = defaultSubgroupId;
          });

          // Create the default subgroup
          layer.subgroups.push({
            id: defaultSubgroupId,
            name: 'Default',
            folderPath: layer.name,
            rarityWeight: 100,
            traits: layer.traits.map(trait => trait.id),
            isLocked: false,
            lockedRarity: 0,
            metadata: {
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          });
        }

        // Add layer to layers array
        layers.push(layer);
      }

      // AŞAMA 5: SON İŞLEMLER
      updateProgress('finalizing', 1, 1, { message: 'Son işlemler yapılıyor...' });

      if (layers.length === 0) {
        throw new Error("No valid layers or traits found. Please check your folder structure and try again.");
      }

      // Update state with new layers
      dispatch({
        type: ActionType.SET_LAYERS,
        payload: layers,
      });

      // Kontrol et: Import başarılı mı?
      if (layers.length > 0) {
        // AŞAMA 6: CONTENT ANALYSIS
        updateProgress('finalizing', 1, 1, { message: 'İçerik analizi yapılıyor...' });

        try {
          // Analyze imported files for trait relationships
          const analysisResults = analyzeImportedFiles(files);

          if (analysisResults.suggestions.length > 0) {
            // Show content analysis results in UI dialog
            const analysisData = {
              relationships: analysisResults.relationships,
              suggestions: analysisResults.suggestions,
              traitFiles: analysisResults.traitFiles,
              timestamp: new Date().toISOString()
            };

            // Show analysis results dialog
            contentAnalysis.finishAnalysis(analysisData);

            // Show toast notification about analysis results
            toast.success(`Content analysis complete! Found ${analysisResults.suggestions.length} rule suggestions.`, {
              id: 'analysis-toast',
              duration: 4000
            });
          }
        } catch (analysisError) {
          console.warn('Content analysis failed:', analysisError);
          // Don't fail the import if analysis fails
        }

        // AŞAMA 7: TAMAMLANDI
        updateProgress('done', 1, 1, { message: 'İçe aktarma tamamlandı!' });

        setImportSuccess(true);
      } else {
        // Import başarısız
        throw new Error("No layers were imported successfully. Please check your folder structure.");
      }
    } catch (error) {
      console.error("Import error:", error);
      setImportError((error as Error).message || "Unknown error during import");

      // İlerleme durumunu hata olarak güncelle
      updateProgress('error', 0, 0, { message: (error as Error).message || "Bilinmeyen hata" });

      // Temizlik işlemleri
      setIsImporting(false);
      cleanupTimeouts();

      // Daha açıklayıcı hata mesajları oluştur
      let errorMessage = (error as Error).message || "Unknown error during import";

      // Dosya formatı hataları için daha iyi mesajlar
      if (errorMessage.includes("No valid layer directories")) {
        errorMessage = "Seçilen klasörde geçerli katman klasörleri bulunamadı. Lütfen doğru klasör yapısını seçtiğinizden emin olun.";
      } else if (errorMessage.includes("No valid layers or traits")) {
        errorMessage = "Geçerli katman veya özellik bulunamadı. Klasör yapısınızı kontrol edin ve tekrar deneyin.";
      } else if (errorMessage.includes("No layers were imported")) {
        errorMessage = "Hiçbir katman içe aktarılamadı. Seçilen klasörlerin doğru yapıda olduğundan emin olun.";
      }

      // Hata mesajı göster
      toast.error(errorMessage, {
        id: 'import-toast',
        duration: 5000 // 5 saniye göster
      });
    }
  }, [dispatch, cleanupTimeouts, setupFailsafeTimeout, updateProgress]);

  return {
    processSelectedFiles,
    isImporting,
    importError,
    progress, // İlerleme durumunu dışarıya aktar
    contentAnalysis // Content analysis hook'unu dışarıya aktar
  };
};

export default useProjectImport;
