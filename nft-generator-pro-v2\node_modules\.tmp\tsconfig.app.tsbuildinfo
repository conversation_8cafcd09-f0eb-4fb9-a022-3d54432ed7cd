{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/application/stores/appstore.ts", "../../src/domain/entities/layer.ts", "../../src/domain/entities/project.ts", "../../src/domain/entities/traitgroup.ts", "../../src/domain/entities/__tests__/project.test.ts", "../../src/infrastructure/services/layerimportservice.ts", "../../src/infrastructure/services/__tests__/layerimport.integration.test.ts", "../../src/infrastructure/services/__tests__/layerimportservice.test.ts", "../../src/infrastructure/services/content-analysis/simple-relationship-analyzer.ts", "../../src/presentation/components/common/applayout.tsx", "../../src/presentation/components/common/contextmenu.tsx", "../../src/presentation/components/common/errorboundary.tsx", "../../src/presentation/components/common/importoptionsdialog.tsx", "../../src/presentation/components/common/renamedialog.tsx", "../../src/presentation/components/common/standardmodal.tsx", "../../src/presentation/components/content-analysis/contentanalysisdialog.tsx", "../../src/presentation/components/layers/editableraritychip.tsx", "../../src/presentation/components/layers/layerspanel.tsx", "../../src/presentation/components/layers/traitgroupitem.tsx", "../../src/presentation/components/layout/mobilelayout.tsx", "../../src/presentation/components/layout/resizablepanel.tsx", "../../src/presentation/components/layout/v1panelcontainer.tsx", "../../src/presentation/components/modals/ruleformeditor.tsx", "../../src/presentation/components/modals/rulesmodal.tsx", "../../src/presentation/components/modals/rulesmodal_simple.tsx", "../../src/presentation/components/modals/settingsmodal.tsx", "../../src/presentation/components/panels/panelheader.tsx", "../../src/presentation/contexts/contentanalysiscontext.tsx", "../../src/presentation/hooks/usecontentanalysis.ts", "../../src/presentation/hooks/useresponsivelayout.ts", "../../src/presentation/pages/homepage.tsx", "../../src/presentation/pages/workspacepage.tsx", "../../src/presentation/theme/themeprovider.tsx", "../../src/presentation/theme/colors.ts", "../../src/presentation/theme/components.ts", "../../src/presentation/theme/css-variables.ts", "../../src/presentation/theme/form-styles.ts", "../../src/presentation/theme/global-styles.ts", "../../src/presentation/theme/index.ts", "../../src/presentation/theme/mui-theme.ts", "../../src/presentation/theme/panel-styles.ts", "../../src/presentation/theme/spacing.ts", "../../src/presentation/theme/typography.ts", "../../src/presentation/theme/utils.ts", "../../src/services/memory/image-persistence.service.ts", "../../src/services/memory/unified-memory.service.ts", "../../src/services/storage/unified-storage.service.ts", "../../src/shared/types/project.types.ts", "../../src/shared/types/rules.types.ts", "../../src/shared/utils/rulesengine.ts", "../../src/shared/utils/rulesutils.ts", "../../src/test/components.integration.test.tsx", "../../src/test/e2e.scenarios.test.tsx", "../../src/test/persistence.test.ts", "../../src/test/setup.ts", "../../src/tests/content-analysis/contentanalysisintegration.test.ts", "../../src/tests/e2e/contentanalysisflow.test.tsx"], "version": "5.8.3"}